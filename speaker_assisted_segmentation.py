# 声纹辅助断句
import numpy as np
from scipy.spatial.distance import cosine

class SpeakerAssistedSegmentation:
    def __init__(self, extractor, similarity_threshold=0.85):
        self.extractor = extractor
        self.similarity_threshold = similarity_threshold
        self.current_speaker_embedding = None
        self.segment_buffer = []
        self.embedding_cache = {}
        
    def analyze_segment_with_speaker_change(self, audio_segment, window_size=0.5):
        """
        分析音频段中的说话人变化，进行精确断句
        
        Args:
            audio_segment: 完整音频段
            window_size: 分析窗口大小（秒）
        
        Returns:
            List[dict]: 断句结果，包含音频段和说话人信息
        """
        sample_rate = 16000
        window_samples = int(window_size * sample_rate)
        overlap_samples = int(0.1 * sample_rate)  # 10%重叠
        
        segments = []
        current_segment_start = 0
        current_speaker = None
        
        # 滑动窗口分析
        for i in range(0, len(audio_segment) - window_samples, window_samples - overlap_samples):
            window_audio = audio_segment[i:i + window_samples]
            
            # 提取声纹特征
            embedding = self._extract_embedding(window_audio)
            speaker_id = self._identify_speaker(embedding)
            
            # 检测说话人变化
            if current_speaker is None:
                current_speaker = speaker_id
                current_segment_start = i
            elif current_speaker != speaker_id:
                # 说话人变化，断句
                segment_audio = audio_segment[current_segment_start:i + overlap_samples]
                segments.append({
                    'audio': segment_audio,
                    'speaker': current_speaker,
                    'start_time': current_segment_start / sample_rate,
                    'end_time': (i + overlap_samples) / sample_rate
                })
                
                current_speaker = speaker_id
                current_segment_start = i
        
        # 添加最后一段
        if current_segment_start < len(audio_segment):
            segment_audio = audio_segment[current_segment_start:]
            segments.append({
                'audio': segment_audio,
                'speaker': current_speaker,
                'start_time': current_segment_start / sample_rate,
                'end_time': len(audio_segment) / sample_rate
            })
        
        return segments
    
    def _extract_embedding(self, audio):
        """提取声纹特征"""
        if len(audio) < 0.3 * 16000:  # 太短的音频
            return None
            
        stream = self.extractor.create_stream()
        stream.accept_waveform(sample_rate=16000, waveform=audio)
        stream.input_finished()
        return np.array(self.extractor.compute(stream))
    
    def _identify_speaker(self, embedding):
        """识别说话人"""
        if embedding is None:
            return "unknown"
            
        # 与已知说话人比较
        best_match = None
        best_similarity = 0
        
        for speaker_id, cached_embedding in self.embedding_cache.items():
            similarity = 1 - cosine(embedding, cached_embedding)
            if similarity > best_similarity and similarity > self.similarity_threshold:
                best_similarity = similarity
                best_match = speaker_id
        
        if best_match is None:
            # 新说话人
            speaker_id = f"发言人{len(self.embedding_cache)}"
            self.embedding_cache[speaker_id] = embedding
            return speaker_id
        
        return best_match
    
    def refine_boundaries(self, segments):
        """精细化边界调整"""
        refined_segments = []
        
        for i, segment in enumerate(segments):
            if i == 0:
                refined_segments.append(segment)
                continue
            
            # 分析相邻段落的边界
            prev_segment = refined_segments[-1]
            boundary_audio = np.concatenate([
                prev_segment['audio'][-int(0.2 * 16000):],  # 前段最后0.2秒
                segment['audio'][:int(0.2 * 16000)]         # 后段前0.2秒
            ])
            
            # 在边界区域寻找最佳切分点
            optimal_cut = self._find_optimal_cut_point(boundary_audio)
            
            # 调整边界
            if optimal_cut is not None:
                adjustment = optimal_cut - int(0.2 * 16000)
                if abs(adjustment) < int(0.1 * 16000):  # 调整幅度不超过0.1秒
                    # 更新前一段
                    prev_segment['audio'] = prev_segment['audio'][:len(prev_segment['audio']) + adjustment]
                    prev_segment['end_time'] += adjustment / 16000
                    
                    # 更新当前段
                    segment['audio'] = segment['audio'][-adjustment:]
                    segment['start_time'] += adjustment / 16000
            
            refined_segments.append(segment)
        
        return refined_segments
    
    def _find_optimal_cut_point(self, boundary_audio):
        """在边界区域找到最佳切分点"""
        # 分析能量变化
        frame_size = int(0.02 * 16000)  # 20ms帧
        energy_profile = []
        
        for i in range(0, len(boundary_audio) - frame_size, frame_size):
            frame = boundary_audio[i:i + frame_size]
            energy = np.sum(frame ** 2)
            energy_profile.append(energy)
        
        if len(energy_profile) < 3:
            return None
        
        # 寻找能量最低点作为切分点
        min_energy_idx = np.argmin(energy_profile)
        return min_energy_idx * frame_size + frame_size // 2
