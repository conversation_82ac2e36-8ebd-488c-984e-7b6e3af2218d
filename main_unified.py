import argparse
import sys
from datetime import datetime
import os

import numpy as np
import sherpa_onnx

try:
    import sounddevice as sd
except ImportError:
    print("请先安装 sounddevice 模块: pip install sounddevice")
    sys.exit(-1)

g_sample_rate = 16000


def get_args():
    parser = argparse.ArgumentParser(
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # --- 声纹识别模型参数 ---
    parser.add_argument(
        "--speaker-embedding-model",
        type=str,
        required=True,
        help="说话人声纹识别模型的路径。",
    )

    # --- VAD 模型参数 ---
    parser.add_argument(
        "--silero-vad-model",
        type=str,
        required=True,
        help="Silero VAD 模型的路径 (silero_vad.onnx)。",
    )

    # --- ASR 模型参数 ---
    parser.add_argument(
        "--asr-encoder-model",
        type=str,
        required=True,
        help="流式 ASR Encoder 模型的路径。",
    )
    parser.add_argument(
        "--asr-decoder-model",
        type=str,
        required=True,
        help="流式 ASR Decoder 模型的路径。",
    )
    parser.add_argument(
        "--asr-joiner-model",
        type=str,
        required=True,
        help="流式 ASR Joiner 模型的路径。",
    )
    parser.add_argument(
        "--asr-tokens",
        type=str,
        required=True,
        help="ASR tokens.txt 文件的路径。",
    )

    # --- 标点符号模型参数 ---
    parser.add_argument(
        "--punct-model-dir",
        type=str,
        help="标点符号模型所在的目录路径 (可选)。",
    )

    # --- 其他参数 ---
    parser.add_argument("--threshold", type=float, default=0.4, help="声纹识别相似度阈值。")
    parser.add_argument("--num-threads", type=int, default=1, help="神经网络计算使用的线程数。")
    parser.add_argument("--debug", type=bool, default=False, help="是否开启 Debug 模式。")
    parser.add_argument("--provider", type=str, default="cuda", help="硬件提供商: cpu, cuda, coreml。")

    return parser.parse_args()


def load_speaker_embedding_model(args):
    config = sherpa_onnx.SpeakerEmbeddingExtractorConfig(
        model=args.speaker_embedding_model,
        num_threads=args.num_threads,
        debug=args.debug,
        provider=args.provider,
    )
    if not config.validate():
        raise ValueError(f"无效的声纹模型配置: {config}")
    return sherpa_onnx.SpeakerEmbeddingExtractor(config)


def create_streaming_asr_model(args):
    """根据命令行参数创建流式ASR模型。"""
    recognizer = sherpa_onnx.OnlineRecognizer.from_transducer(
        encoder=args.asr_encoder_model,
        decoder=args.asr_decoder_model,
        joiner=args.asr_joiner_model,
        tokens=args.asr_tokens,
        sample_rate=g_sample_rate,
        num_threads=args.num_threads,
        provider=args.provider,
    )
    return recognizer


def create_punctuation_model(args):
    """根据命令行参数创建标点符号模型。"""
    if not args.punct_model_dir or not os.path.isdir(args.punct_model_dir):
        print("未提供标点符号模型目录，将不使用标点功能。")
        return None
    
    model_path = os.path.join(args.punct_model_dir, "model.onnx")
    vocab_path = os.path.join(args.punct_model_dir, "tokens.json")

    if not os.path.exists(model_path) or not os.path.exists(vocab_path):
        print(f"警告: 在目录 {args.punct_model_dir} 中未找到 model.onnx 或 tokens.json。")
        print("将不使用标点功能。")
        return None

    try:
        # 修复点：将 `model` 参数修改为 `model_config`
        config = sherpa_onnx.OnlinePunctuationConfig(
            model_config=sherpa_onnx.OnlinePunctuationModelConfig(
                cnn_bilstm=model_path,
                bpe_vocab=vocab_path,
            )
        )
        if not config.validate():
            raise ValueError("无效的标点模型配置")
        return sherpa_onnx.OnlinePunctuation(config)
    except Exception as e:
        print(f"警告: 加载标点模型失败: {e}")
        return None


def main():
    args = get_args()
    print("运行参数:", args)

    devices = sd.query_devices()
    if not devices:
        print("未找到麦克风设备")
        sys.exit(0)
    print("可用设备列表:\n", devices)
    default_input_device_idx = sd.default.device[0]
    print(f'使用默认设备: {devices[default_input_device_idx]["name"]}')

    print("正在加载声纹识别模型...")
    extractor = load_speaker_embedding_model(args)
    print("✅ 声纹识别模型加载成功")

    print("正在加载ASR模型...")
    asr_model = create_streaming_asr_model(args)
    print("✅ ASR模型加载成功")

    print("正在加载标点符号模型...")
    punct_model = create_punctuation_model(args)
    if punct_model:
        print("✅ 标点符号模型加载成功")

    manager = sherpa_onnx.SpeakerEmbeddingManager(extractor.dim)

    vad_config = sherpa_onnx.VadModelConfig()
    vad_config.silero_vad.model = args.silero_vad_model
    vad_config.silero_vad.min_silence_duration = 0.25
    vad_config.silero_vad.min_speech_duration = 1.0
    vad_config.sample_rate = g_sample_rate

    window_size = vad_config.silero_vad.window_size
    vad = sherpa_onnx.VoiceActivityDetector(vad_config, buffer_size_in_seconds=100)

    samples_per_read = int(0.1 * g_sample_rate)

    print("\n--- 系统就绪, 请开始说话 ---")

    speaker_id = 0
    buffer = np.array([], dtype=np.float32)
    with sd.InputStream(channels=1, dtype="float32", samplerate=g_sample_rate) as s:
        while True:
            samples, _ = s.read(samples_per_read)
            samples = samples.flatten()
            buffer = np.concatenate([buffer, samples])
            while len(buffer) > window_size:
                vad.accept_waveform(buffer[:window_size])
                buffer = buffer[window_size:]

            while not vad.empty():
                speech_segment = np.array(vad.front.samples, dtype=np.float32)
                vad.pop()

                if len(speech_segment) < 0.5 * g_sample_rate:
                    continue

                # 声纹识别
                spk_stream = extractor.create_stream()
                spk_stream.accept_waveform(sample_rate=g_sample_rate, waveform=speech_segment)
                spk_stream.input_finished()
                embedding = np.array(extractor.compute(spk_stream))
                
                name = manager.search(embedding, threshold=args.threshold)
                if not name:
                    name = f"说话人_{speaker_id}"
                    manager.add(name, embedding)
                    speaker_id += 1

                # ASR转录
                try:
                    asr_stream = asr_model.create_stream()
                    asr_stream.accept_waveform(sample_rate=g_sample_rate, waveform=speech_segment)
                    asr_stream.input_finished()
                    
                    while asr_model.is_ready(asr_stream):
                        asr_model.decode_stream(asr_stream)
                    
                    result = asr_model.get_result(asr_stream)
                    text = result.text.strip()
                    
                    if not text:
                        text = "[无识别结果]"
                    elif punct_model:
                        try:
                            text = punct_model.add_punctuation(text)
                        except Exception as e:
                            print(f"标点处理错误: {e}")

                except Exception as e:
                    text = f"[ASR错误: {e}]"
                    print(f"ASR处理错误: {e}")

                timestamp = datetime.now().strftime('%H:%M:%S')
                print(f"[{timestamp}] {name}: {text}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n捕获到 Ctrl+C, 正在退出")