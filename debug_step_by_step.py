#!/usr/bin/env python3

def step1():
    print("Step 1: Basic imports")
    import os
    import sys
    print("✅ Basic imports OK")

def step2():
    print("Step 2: Import sherpa_onnx")
    import sherpa_onnx
    print("✅ sherpa_onnx import OK")

def step3():
    print("Step 3: Check files")
    import os
    punct_dir = r".\model\punctuation"
    model_path = os.path.join(punct_dir, "model.onnx")
    vocab_path = os.path.join(punct_dir, "tokens.json")
    print(f"Model exists: {os.path.exists(model_path)}")
    print(f"Vocab exists: {os.path.exists(vocab_path)}")
    print("✅ File check OK")

def step4():
    print("Step 4: Check sherpa_onnx classes")
    import sherpa_onnx
    
    # 检查是否有标点相关类
    if hasattr(sherpa_onnx, 'OnlinePunctuation'):
        print("✅ OnlinePunctuation found")
    else:
        print("❌ OnlinePunctuation NOT found")
        
    if hasattr(sherpa_onnx, 'OnlinePunctuationConfig'):
        print("✅ OnlinePunctuationConfig found")
    else:
        print("❌ OnlinePunctuationConfig NOT found")

def step5():
    print("Step 5: Try creating config")
    import sherpa_onnx
    import os
    
    model_path = os.path.join(r".\model\punctuation", "model.onnx")
    vocab_path = os.path.join(r".\model\punctuation", "tokens.json")
    
    try:
        config = sherpa_onnx.OnlinePunctuationConfig(
            model_config=sherpa_onnx.OnlinePunctuationModelConfig(
                cnn_bilstm=model_path,
                bpe_vocab=vocab_path,
            )
        )
        print("✅ Config created OK")
    except Exception as e:
        print(f"❌ Config creation failed: {e}")
        raise

if __name__ == "__main__":
    try:
        step1()
        step2()
        step3()
        step4()
        step5()
        print("✅ All steps completed successfully!")
    except Exception as e:
        print(f"❌ Failed at step: {e}")
        import traceback
        traceback.print_exc()

