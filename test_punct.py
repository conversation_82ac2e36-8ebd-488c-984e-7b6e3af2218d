#!/usr/bin/env python3
"""测试标点符号模型加载"""

import os
import sys

def test_punct_model():
    print("=== 标点符号模型加载测试 ===")
    
    # 1. 检查模型文件是否存在
    punct_dir = r".\model\punctuation"
    model_path = os.path.join(punct_dir, "model.onnx")
    vocab_path = os.path.join(punct_dir, "tokens.json")
    
    print(f"标点模型目录: {os.path.abspath(punct_dir)}")
    print(f"模型文件存在: {os.path.exists(model_path)}")
    print(f"词汇文件存在: {os.path.exists(vocab_path)}")
    
    if not os.path.exists(model_path):
        print("❌ 模型文件不存在!")
        return False
    if not os.path.exists(vocab_path):
        print("❌ 词汇文件不存在!")
        return False
        
    # 2. 检查sherpa_onnx可用的类
    try:
        import sherpa_onnx
        print(f"sherpa_onnx 导入成功")
        
        # 检查所有相关类
        all_attrs = [x for x in dir(sherpa_onnx) if not x.startswith('_')]
        print(f"sherpa_onnx 包含 {len(all_attrs)} 个公共属性/类")
        
        # 查找可能的标点相关类
        punct_classes = []
        for attr in all_attrs:
            if 'punct' in attr.lower() or 'Punct' in attr:
                punct_classes.append(attr)
        
        print(f"标点相关类: {punct_classes}")
        
        # 检查常见的标点类
        classes_to_check = [
            'OnlinePunctuation', 'OnlinePunctuationConfig', 
            'OnlinePunctuationModelConfig', 'OfflinePunctuation',
            'PunctuationModel', 'Punctuation'
        ]
        
        available_classes = []
        for cls_name in classes_to_check:
            if hasattr(sherpa_onnx, cls_name):
                available_classes.append(cls_name)
                
        print(f"可用的标点类: {available_classes}")
        
        if not available_classes:
            print("❌ 未找到标点符号相关的类!")
            print("可能的原因:")
            print("1. sherpa_onnx版本不支持标点功能")
            print("2. 需要安装额外的标点模块")
            return False
            
    except ImportError as e:
        print(f"❌ sherpa_onnx 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查sherpa_onnx时出错: {e}")
        return False
        
    # 3. 尝试使用原代码逻辑
    try:
        print("\n=== 尝试原代码逻辑 ===")
        config = sherpa_onnx.OnlinePunctuationConfig(
            model_config=sherpa_onnx.OnlinePunctuationModelConfig(
                cnn_bilstm=model_path,
                bpe_vocab=vocab_path,
            )
        )
        print("配置创建成功")
        
        if not config.validate():
            print("❌ 配置验证失败")
            return False
            
        punct_model = sherpa_onnx.OnlinePunctuation(config)
        print("✅ 标点模型创建成功!")
        return True
        
    except AttributeError as e:
        print(f"❌ 属性错误: {e}")
        print("这表示您的sherpa_onnx版本不支持OnlinePunctuation")
        return False
    except Exception as e:
        print(f"❌ 模型创建失败: {e}")
        return False

if __name__ == "__main__":
    success = test_punct_model()
    if success:
        print("\n✅ 标点模型测试通过!")
    else:
        print("\n❌ 标点模型测试失败!")
        sys.exit(1)

