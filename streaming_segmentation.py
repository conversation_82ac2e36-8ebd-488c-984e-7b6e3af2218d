# 实时流式断句处理
import numpy as np
from collections import deque
import time

class StreamingSegmentation:
    def __init__(self, vad, extractor, asr_model, sample_rate=16000):
        self.vad = vad
        self.extractor = extractor
        self.asr_model = asr_model
        self.sample_rate = sample_rate
        
        # 缓冲区管理
        self.audio_buffer = deque(maxlen=int(10 * sample_rate))  # 10秒缓冲
        self.processing_buffer = []
        
        # 说话人跟踪
        self.current_speaker = None
        self.speaker_embeddings = {}
        self.speaker_confidence_threshold = 0.8
        
        # 时间戳管理
        self.last_speech_end = 0
        self.current_segment_start = 0
        
        # 状态管理
        self.in_speech = False
        self.pending_segments = []
        
    def process_audio_chunk(self, audio_chunk, timestamp):
        """
        处理实时音频块
        
        Args:
            audio_chunk: 音频数据
            timestamp: 时间戳
        """
        # 添加到缓冲区
        self.audio_buffer.extend(audio_chunk)
        
        # VAD检测
        vad_result = self._run_vad_on_chunk(audio_chunk)
        
        if vad_result['has_speech']:
            if not self.in_speech:
                # 语音开始
                self.in_speech = True
                self.current_segment_start = timestamp
                self.processing_buffer = []
            
            # 累积语音数据
            self.processing_buffer.extend(audio_chunk)
            
            # 实时说话人检测
            if len(self.processing_buffer) >= int(0.5 * self.sample_rate):
                speaker_change = self._detect_speaker_change()
                if speaker_change:
                    # 说话人变化，强制断句
                    self._force_segment_break(timestamp)
        
        else:
            if self.in_speech:
                # 语音结束
                self.in_speech = False
                self._finalize_segment(timestamp)
        
        # 处理待处理的段落
        self._process_pending_segments()
    
    def _run_vad_on_chunk(self, audio_chunk):
        """对音频块运行VAD"""
        # 这里简化处理，实际应该使用VAD模型
        energy = np.mean(audio_chunk ** 2)
        has_speech = energy > 0.001  # 简单的能量阈值
        
        return {
            'has_speech': has_speech,
            'confidence': min(energy * 1000, 1.0)
        }
    
    def _detect_speaker_change(self):
        """检测说话人变化"""
        if len(self.processing_buffer) < int(0.5 * self.sample_rate):
            return False
        
        # 提取最近0.5秒的声纹特征
        recent_audio = np.array(self.processing_buffer[-int(0.5 * self.sample_rate):])
        embedding = self._extract_embedding(recent_audio)
        
        if embedding is None:
            return False
        
        # 与当前说话人比较
        if self.current_speaker is not None:
            current_embedding = self.speaker_embeddings.get(self.current_speaker)
            if current_embedding is not None:
                similarity = self._calculate_similarity(embedding, current_embedding)
                if similarity < self.speaker_confidence_threshold:
                    return True
        
        return False
    
    def _force_segment_break(self, timestamp):
        """强制断句"""
        if len(self.processing_buffer) > int(0.3 * self.sample_rate):
            # 创建段落
            segment = {
                'audio': np.array(self.processing_buffer[:-int(0.2 * self.sample_rate)]),
                'start_time': self.current_segment_start,
                'end_time': timestamp - 0.2,
                'speaker': self.current_speaker,
                'forced_break': True
            }
            self.pending_segments.append(segment)
            
            # 保留重叠部分作为新段落的开始
            self.processing_buffer = self.processing_buffer[-int(0.2 * self.sample_rate):]
            self.current_segment_start = timestamp - 0.2
    
    def _finalize_segment(self, timestamp):
        """完成段落"""
        if len(self.processing_buffer) > int(0.3 * self.sample_rate):
            segment = {
                'audio': np.array(self.processing_buffer),
                'start_time': self.current_segment_start,
                'end_time': timestamp,
                'speaker': None,  # 待识别
                'forced_break': False
            }
            self.pending_segments.append(segment)
        
        self.processing_buffer = []
        self.last_speech_end = timestamp
    
    def _process_pending_segments(self):
        """处理待处理的段落"""
        processed_segments = []
        
        for segment in self.pending_segments:
            # 声纹识别
            if segment['speaker'] is None:
                embedding = self._extract_embedding(segment['audio'])
                speaker_id = self._identify_speaker(embedding)
                segment['speaker'] = speaker_id
                self.current_speaker = speaker_id
            
            # ASR转录
            text = self._transcribe_audio(segment['audio'])
            segment['text'] = text
            
            # 输出结果
            self._output_segment(segment)
            processed_segments.append(segment)
        
        # 清空已处理的段落
        self.pending_segments = []
    
    def _extract_embedding(self, audio):
        """提取声纹特征"""
        if len(audio) < int(0.3 * self.sample_rate):
            return None
        
        stream = self.extractor.create_stream()
        stream.accept_waveform(sample_rate=self.sample_rate, waveform=audio)
        stream.input_finished()
        return np.array(self.extractor.compute(stream))
    
    def _identify_speaker(self, embedding):
        """识别说话人"""
        if embedding is None:
            return "unknown"
        
        best_match = None
        best_similarity = 0
        
        for speaker_id, cached_embedding in self.speaker_embeddings.items():
            similarity = self._calculate_similarity(embedding, cached_embedding)
            if similarity > best_similarity and similarity > self.speaker_confidence_threshold:
                best_similarity = similarity
                best_match = speaker_id
        
        if best_match is None:
            speaker_id = f"发言人{len(self.speaker_embeddings)}"
            self.speaker_embeddings[speaker_id] = embedding
            return speaker_id
        
        return best_match
    
    def _calculate_similarity(self, emb1, emb2):
        """计算声纹相似度"""
        from scipy.spatial.distance import cosine
        return 1 - cosine(emb1, emb2)
    
    def _transcribe_audio(self, audio):
        """ASR转录"""
        try:
            stream = self.asr_model.create_stream()
            stream.accept_waveform(sample_rate=self.sample_rate, waveform=audio)
            stream.input_finished()
            
            while self.asr_model.is_ready(stream):
                self.asr_model.decode_stream(stream)
            
            result = self.asr_model.get_result(stream)
            if isinstance(result, str):
                return result.strip()
            else:
                return result.text.strip()
        except Exception as e:
            return f"[转录错误: {e}]"
    
    def _output_segment(self, segment):
        """输出段落结果"""
        timestamp = time.strftime('%H:%M:%S', time.localtime(segment['start_time']))
        duration = segment['end_time'] - segment['start_time']
        break_type = " [强制断句]" if segment['forced_break'] else ""
        
        print(f"[{timestamp}] {segment['speaker']} ({duration:.1f}s){break_type}: {segment['text']}")
