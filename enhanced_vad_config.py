# 动态VAD配置示例
import numpy as np
import sherpa_onnx

class AdaptiveVADConfig:
    def __init__(self, base_config):
        self.base_config = base_config
        self.conversation_mode = False
        self.recent_speech_intervals = []
        self.adaptive_silence_duration = 0.25
        
    def update_conversation_context(self, speech_intervals):
        """根据最近的语音间隔调整VAD参数"""
        self.recent_speech_intervals = speech_intervals[-10:]  # 保留最近10次
        
        if len(self.recent_speech_intervals) >= 3:
            avg_interval = np.mean(self.recent_speech_intervals)
            if avg_interval < 0.5:  # 快速对话
                self.adaptive_silence_duration = max(0.1, avg_interval * 0.8)
                self.conversation_mode = True
            else:
                self.adaptive_silence_duration = 0.25
                self.conversation_mode = False
    
    def get_vad_config(self):
        """获取自适应VAD配置"""
        config = sherpa_onnx.VadModelConfig()
        config.silero_vad.model = self.base_config.silero_vad.model
        config.silero_vad.min_silence_duration = self.adaptive_silence_duration
        config.silero_vad.min_speech_duration = 0.3 if self.conversation_mode else 1.0
        config.sample_rate = self.base_config.sample_rate
        return config

# 使用示例
def create_adaptive_vad(args):
    base_config = sherpa_onnx.VadModelConfig()
    base_config.silero_vad.model = args.silero_vad_model
    base_config.sample_rate = 16000
    
    adaptive_config = AdaptiveVADConfig(base_config)
    return adaptive_config
